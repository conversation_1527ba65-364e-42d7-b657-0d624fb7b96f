# Flavors no Aplicativo

Este aplicativo suporta múltiplos "flavors" (variantes) para facilitar o white labeling. Atualmente, temos dois flavors configurados:

1. **Zionek** (padrão) - Tema azul escuro (#203759) e amarelo (#E9C46A)
2. **PulsePoint** - Tema azul royal e roxo

## Como Executar Diferentes Flavors

### Usando o Script build_flavor.bat

O script `build_flavor.bat` facilita a construção e execução do aplicativo com diferentes flavors:

```
build_flavor.bat [flavor] [platform] [mode] [action]
```

Onde:
- `flavor`: `zionek` (padrão) ou `pulsepoint`
- `platform`: `android` (padrão), `ios` ou `all`
- `mode`: `debug` (padrão) ou `release`
- `action`: `build` (padrão) ou `run`

Exemplos:

```
# Construir o flavor Zionek para Android em modo debug
build_flavor.bat zionek android debug build

# Executar o flavor PulsePoint em modo debug
build_flavor.bat pulsepoint android debug run

# Construir o flavor Zionek para iOS em modo release
build_flavor.bat zionek ios release build
```

### Usando o VS Code

1. Abra o arquivo de configuração de execução `.vscode/launch.json`
2. Adicione as seguintes configurações:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Zionek",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_zionek.dart",
      "args": ["--flavor", "zionek"]
    },
    {
      "name": "PulsePoint",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_pulsepoint.dart",
      "args": ["--flavor", "pulsepoint"]
    }
  ]
}
```

3. Selecione o flavor desejado no menu de execução do VS Code

### Usando a Linha de Comando

Para executar o flavor Zionek:
```
flutter run --flavor zionek -t lib/main_zionek.dart
```

Para executar o flavor PulsePoint:
```
flutter run --flavor pulsepoint -t lib/main_pulsepoint.dart
```

## Configuração de Flavors

Os flavors são configurados em vários arquivos:

1. `lib/utils/flavor_config.dart` - Define a enumeração de flavors e a configuração
2. `lib/main_zionek.dart` e `lib/main_pulsepoint.dart` - Pontos de entrada específicos para cada flavor
3. `lib/main_common.dart` - Código comum compartilhado entre todos os flavors
4. `lib/utils/app_strings.dart` - Strings específicas para cada flavor
5. `lib/utils/app_theme.dart` - Temas e cores específicos para cada flavor

## Adicionando um Novo Flavor

Para adicionar um novo flavor:

1. Adicione o novo flavor à enumeração `Flavor` em `lib/utils/flavor_config.dart`
2. Adicione as strings específicas do flavor em `lib/utils/app_strings.dart`
3. Adicione as cores específicas do flavor em `lib/utils/app_theme.dart`
4. Crie um novo arquivo de ponto de entrada, por exemplo, `lib/main_new_flavor.dart`
5. Atualize o arquivo `flutter_flavors.yaml` com as configurações do novo flavor

## Construindo para Produção

Para construir uma versão de produção para um flavor específico:

### Android

```
flutter build apk --flavor zionek -t lib/main_zionek.dart
flutter build apk --flavor pulsepoint -t lib/main_pulsepoint.dart
```

### iOS

```
flutter build ios --flavor zionek -t lib/main_zionek.dart
flutter build ios --flavor pulsepoint -t lib/main_pulsepoint.dart
```

## Observações

- Para uma configuração completa de flavors no Android e iOS, é necessário configurar os arquivos `android/app/build.gradle` e `ios/Runner.xcodeproj`
- Para ícones e recursos específicos de cada flavor, é necessário configurar os diretórios de recursos correspondentes

## Ícones do Aplicativo

Os ícones do aplicativo são configurados para cada flavor:

1. Os ícones para o Android estão localizados em:
   - Zionek:
     - Ícones PNG: `android/app/src/zionek/res/mipmap-*/ic_launcher.png`

   - PulsePoint:
     - Ícones PNG: `android/app/src/pulsepoint/res/mipmap-*/ic_launcher.png`

2. Os ícones para o Flutter estão localizados em:
   - Zionek: `assets/icons/zionek/zionek_icon.png`
   - PulsePoint: `assets/icons/pulsepoint/pulsepoint_icon.png`

Para substituir os ícones, você pode:
1. Substituir os arquivos PNG nas pastas mipmap para os ícones do Android
2. Substituir os arquivos PNG nas pastas de assets para os ícones do Flutter

Nota: Para dispositivos Android 8.0 (API 26) ou superior, você pode usar ícones adaptativos, mas isso requer configuração adicional.
