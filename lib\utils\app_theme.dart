import 'package:flutter/material.dart';

/// Classe que centraliza todos os temas e cores do aplicativo
/// Isso facilita a personalização para white labels
class AppTheme {
  // Singleton pattern
  static final AppTheme _instance = AppTheme._internal();
  factory AppTheme() => _instance;
  AppTheme._internal();

  // Variável para armazenar o white label atual
  String _currentWhiteLabel = 'default';

  // Getter para o white label atual
  String get currentWhiteLabel => _currentWhiteLabel;

  // Método para definir o white label
  void setWhiteLabel(String whiteLabel) {
    _currentWhiteLabel = whiteLabel;
  }

  // Cores primárias para cada white label
  Color get primaryColor => _getColorForWhiteLabel({
    'default': const Color(0xFF203759), // Azul escuro para Zionek
    'pulsepoint': const Color(0xFF4169E1), // Royal Blue para PulsePoint
    'other_brand': const Color(0xFF6200EE), // Purple para outra marca
  });

  // Cores secundárias para cada white label
  Color get secondaryColor => _getColorForWhiteLabel({
    'default': const Color(0xFFE9C46A), // Amarelo para Zionek
    'pulsepoint': const Color(0xFF8A2BE2), // Purple para PulsePoint
    'other_brand': const Color(0xFF03DAC6), // Teal para outra marca
  });

  // Cores de fundo para cada white label
  Color get backgroundColor => _getColorForWhiteLabel({
    'default': Colors.white, // Branco para Zionek
    'pulsepoint': Colors.white,
    'other_brand': Colors.white,
  });

  // Cores de fundo escuro para cada white label (para a tela de vídeos)
  Color get darkBackgroundColor => _getColorForWhiteLabel({
    'default': const Color(0xFF1A2332), // Azul escuro para Zionek
    'pulsepoint': const Color(0xFF1E2A5E), // Azul royal escuro para PulsePoint
    'other_brand': const Color(0xFF121212), // Cinza escuro para outra marca
  });

  // Cores de texto para cada white label
  Color get textColor => _getColorForWhiteLabel({
    'default': const Color(0xFF203759), // Azul escuro para Zionek (mesmo que primária)
    'pulsepoint': const Color(0xFF333333),
    'other_brand': const Color(0xFF212121),
  });

  // Cores de erro para cada white label
  Color get errorColor => _getColorForWhiteLabel({
    'default': const Color(0xFFE63946), // Vermelho para Zionek
    'pulsepoint': Colors.red,
    'other_brand': const Color(0xFFB00020),
  });

  // Cores de botão para cada white label
  Color get buttonColor => _getColorForWhiteLabel({
    'default': const Color(0xFF203759), // Azul escuro para Zionek
    'pulsepoint': const Color(0xFF4169E1), // Royal Blue para PulsePoint
    'other_brand': const Color(0xFF6200EE), // Purple para outra marca
  });

  // Cores de texto de botão para cada white label
  Color get buttonTextColor => _getColorForWhiteLabel({
    'default': Colors.white, // Branco para Zionek
    'pulsepoint': Colors.white,
    'other_brand': Colors.white,
  });

  // Cores de borda para cada white label
  Color get borderColor => _getColorForWhiteLabel({
    'default': const Color(0xFFE9C46A).withOpacity(0.5), // Amarelo com opacidade para Zionek
    'pulsepoint': Colors.grey,
    'other_brand': Colors.grey.shade300,
  });

  // Cores de foco para cada white label
  Color get focusColor => _getColorForWhiteLabel({
    'default': const Color(0xFFE9C46A), // Amarelo para Zionek
    'pulsepoint': const Color(0xFF4169E1),
    'other_brand': const Color(0xFF6200EE),
  });

  // Obter o tema completo para o white label atual
  ThemeData getTheme() {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        primary: primaryColor,
        secondary: secondaryColor,
        background: backgroundColor,
        error: errorColor,
      ),
      scaffoldBackgroundColor: backgroundColor,
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: textColor),
        bodyMedium: TextStyle(color: textColor),
        titleLarge: TextStyle(color: textColor),
      ),
      useMaterial3: true,
      inputDecorationTheme: InputDecorationTheme(
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: focusColor, width: 2.0),
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: borderColor),
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: errorColor),
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: errorColor, width: 2.0),
          borderRadius: const BorderRadius.all(Radius.circular(8.0)),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          foregroundColor: buttonTextColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: buttonTextColor,
      ),
    );
  }

  // Método auxiliar para obter a cor correta com base no white label atual
  Color _getColorForWhiteLabel(Map<String, Color> colors) {
    return colors[_currentWhiteLabel] ?? colors['default'] ?? Colors.black;
  }
}
