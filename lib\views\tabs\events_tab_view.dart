import 'package:flutter/material.dart';
import '../../utils/app_strings.dart';
import '../../utils/app_theme.dart';

class EventsTabView extends StatelessWidget {
  const EventsTabView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final strings = AppStrings();
    final appTheme = AppTheme();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Eventos',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: appTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.event_note,
                    size: 100,
                    color: appTheme.primaryColor.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    strings.featureNotImplemented,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: appTheme.textColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
