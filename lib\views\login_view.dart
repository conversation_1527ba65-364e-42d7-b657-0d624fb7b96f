import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/validators.dart';
import '../utils/app_strings.dart';
import '../utils/app_theme.dart';
import '../utils/image_helper.dart';
import '../viewmodels/base_viewmodel.dart';
import '../viewmodels/login_viewmodel.dart';
import 'home_view.dart';

class LoginView extends StatefulWidget {
  const LoginView({Key? key}) : super(key: key);

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  bool _obscurePassword = true;

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<LoginViewModel>(context);
    final screenSize = MediaQuery.of(context).size;
    final strings = AppStrings();
    final appTheme = AppTheme();
    final imageHelper = ImageHelper();

    return Scaffold(
      body: Stack(
        children: [
          // Top background image
          Positioned(
            top: 0,
            right: 0,
            child: Image.asset(
              imageHelper.topBackgroundPath,
              width: screenSize.width * 0.7,
            ),
          ),

          // Bottom background image
          Positioned(
            bottom: 0,
            left: 0,
            child: Image.asset(
              imageHelper.bottomBackgroundPath,
              width: screenSize.width,
            ),
          ),

          // Main content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Center(
                child: SingleChildScrollView(
                  child: Form(
                    key: viewModel.formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Logo
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Image.asset(
                            imageHelper.logoPath,
                            height: 30,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Login text
                        Text(
                          strings.loginTitle,
                          style: TextStyle(
                            fontSize: 18,
                            color: appTheme.textColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Email field
                        TextFormField(
                          controller: viewModel.emailController,
                          decoration: InputDecoration(
                            labelText: strings.emailLabel,
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8.0)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          ),
                          keyboardType: TextInputType.emailAddress,
                          validator: Validators.validateEmail,
                          enabled: !viewModel.isBusy,
                        ),
                        const SizedBox(height: 16),

                        // Password field
                        TextFormField(
                          controller: viewModel.passwordController,
                          decoration: InputDecoration(
                            labelText: strings.passwordLabel,
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8.0)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword ? Icons.visibility_off : Icons.visibility,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                          obscureText: _obscurePassword,
                          validator: Validators.validatePassword,
                          enabled: !viewModel.isBusy,
                        ),
                        const SizedBox(height: 24),

                        // Error message
                        if (viewModel.state == ViewState.error)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Text(
                              viewModel.errorMessage,
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                          ),

                        // Login button
                        ElevatedButton(
                          onPressed: viewModel.isBusy
                              ? null
                              : () async {
                                  if (await viewModel.login()) {
                                    if (context.mounted) {
                                      Navigator.of(context).pushReplacement(
                                        MaterialPageRoute(
                                          builder: (context) => const HomeView(),
                                        ),
                                      );
                                    }
                                  }
                                },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: appTheme.buttonColor,
                            foregroundColor: appTheme.buttonTextColor,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: viewModel.isBusy
                              ? SizedBox(
                                  width: 25, // largura desejada
                                  height: 25, // altura desejada
                                  child: CircularProgressIndicator(),
                                )
                              : Text(
                                  strings.loginButtonText,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                        const SizedBox(height: 16),

                        // Forgot password
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              strings.forgotPasswordText,
                              style: TextStyle(
                                color: appTheme.textColor.withOpacity(0.7),
                              ),
                            ),
                            TextButton(
                              onPressed: viewModel.isBusy
                                  ? null
                                  : () {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: Text(strings.forgotPasswordNotImplemented),
                                        ),
                                      );
                                    },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                foregroundColor: appTheme.primaryColor,
                              ),
                              child: Text(
                                strings.recoverPasswordText,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        // Demo credentials (for development only)
                        const SizedBox(height: 32),
                        Text(
                          strings.demoCredentials,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: appTheme.textColor.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
