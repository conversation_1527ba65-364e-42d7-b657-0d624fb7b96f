import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../models/bible_models.dart';

/// Serviço para carregar e gerenciar dados da Bíblia
class BibleService {
  static final BibleService _instance = BibleService._internal();
  factory BibleService() => _instance;
  BibleService._internal();

  Bible? _bible;
  bool _isLoaded = false;

  // URL base da API da Bíblia
  static const String _baseUrl = 'https://raw.githubusercontent.com/MaatheusGois/bible/main/versions/pt-br/arc';

  // Cache para livros carregados
  final Map<String, BibleBook> _booksCache = {};

  /// Carrega a Bíblia completa da API (apenas metadados dos livros)
  Future<Bible> loadBible() async {
    if (_isLoaded && _bible != null) {
      return _bible!;
    }

    try {
      // Carrega apenas os metadados dos livros (sem conteúdo)
      final books = _createBooksMetadata();
      _bible = Bible(
        version: 'Almeida Revista e Corrigida',
        books: books,
      );
      _isLoaded = true;
      return _bible!;
    } catch (e) {
      throw Exception('Erro ao carregar a Bíblia: $e');
    }
  }

  /// Cria metadados dos livros sem carregar o conteúdo
  List<BibleBook> _createBooksMetadata() {
    final List<BibleBook> books = [];
    final booksInfo = _getBooksInfo();

    for (final bookInfo in booksInfo) {
      // Cria livro vazio (será carregado sob demanda)
      books.add(BibleBook(
        name: bookInfo['name'],
        abbreviation: bookInfo['abbreviation'],
        isOldTestament: bookInfo['isOldTestament'],
        chapters: [], // Vazio inicialmente
      ));
    }

    return books;
  }

  /// Carrega um livro específico sob demanda
  Future<BibleBook> loadBookContent(String bookName) async {
    // Encontra o ID do livro
    final booksInfo = _getBooksInfo();
    final bookInfo = booksInfo.firstWhere(
      (info) => info['name'] == bookName,
      orElse: () => throw Exception('Livro não encontrado: $bookName'),
    );

    return await _loadBook(
      bookInfo['id'],
      bookInfo['name'],
      bookInfo['abbreviation'],
      bookInfo['isOldTestament']
    );
  }

  /// Carrega um livro específico da API
  Future<BibleBook> _loadBook(String bookId, String name, String abbreviation, bool isOldTestament) async {
    if (_booksCache.containsKey(bookId)) {
      return _booksCache[bookId]!;
    }

    try {
      final url = '$_baseUrl/$bookId/$bookId.json';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<BibleChapter> chapters = [];

        // Processa os capítulos (formato da API: array de arrays de strings)
        final List<dynamic> chaptersData = data['chapters'];

        for (int chapterIndex = 0; chapterIndex < chaptersData.length; chapterIndex++) {
          final List<dynamic> versesData = chaptersData[chapterIndex];
          final List<BibleVerse> verses = [];

          // Processa os versículos (cada item é uma string)
          for (int verseIndex = 0; verseIndex < versesData.length; verseIndex++) {
            verses.add(BibleVerse(
              number: verseIndex + 1, // Número do versículo baseado no índice
              text: versesData[verseIndex] as String,
            ));
          }

          chapters.add(BibleChapter(
            number: chapterIndex + 1, // Número do capítulo baseado no índice
            verses: verses,
          ));
        }

        final book = BibleBook(
          name: name,
          abbreviation: abbreviation,
          isOldTestament: isOldTestament,
          chapters: chapters,
        );

        _booksCache[bookId] = book;
        return book;
      } else {
        throw Exception('Falha ao carregar livro: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro ao carregar livro $name: $e');
    }
  }

  /// Busca versículos por texto
  Future<List<BibleSearchResult>> searchVerses(String query) async {
    if (query.trim().isEmpty) return [];

    final bible = await loadBible();
    final List<BibleSearchResult> results = [];
    final String lowerQuery = query.toLowerCase();

    for (final book in bible.books) {
      for (final chapter in book.chapters) {
        for (final verse in chapter.verses) {
          if (verse.text.toLowerCase().contains(lowerQuery)) {
            results.add(BibleSearchResult(
              book: book,
              chapter: chapter,
              verse: verse,
            ));
          }
        }
      }
    }

    return results;
  }

  /// Busca um livro específico por nome ou abreviação
  Future<BibleBook?> findBook(String nameOrAbbreviation) async {
    final bible = await loadBible();
    final String query = nameOrAbbreviation.toLowerCase();

    for (final book in bible.books) {
      if (book.name.toLowerCase() == query || 
          book.abbreviation.toLowerCase() == query) {
        return book;
      }
    }

    return null;
  }

  /// Busca um capítulo específico de um livro
  Future<BibleChapter?> findChapter(String bookName, int chapterNumber) async {
    final book = await findBook(bookName);
    if (book == null) return null;

    for (final chapter in book.chapters) {
      if (chapter.number == chapterNumber) {
        return chapter;
      }
    }

    return null;
  }

  /// Busca um versículo específico
  Future<BibleVerse?> findVerse(String bookName, int chapterNumber, int verseNumber) async {
    final chapter = await findChapter(bookName, chapterNumber);
    if (chapter == null) return null;

    for (final verse in chapter.verses) {
      if (verse.number == verseNumber) {
        return verse;
      }
    }

    return null;
  }

  /// Obtém todos os livros
  Future<List<BibleBook>> getAllBooks() async {
    final bible = await loadBible();
    return bible.books;
  }

  /// Obtém livros do Antigo Testamento
  Future<List<BibleBook>> getOldTestamentBooks() async {
    final bible = await loadBible();
    return bible.oldTestamentBooks;
  }

  /// Obtém livros do Novo Testamento
  Future<List<BibleBook>> getNewTestamentBooks() async {
    final bible = await loadBible();
    return bible.newTestamentBooks;
  }

  /// Verifica se a Bíblia está carregada
  bool get isLoaded => _isLoaded;

  /// Obtém a versão da Bíblia
  String? get version => _bible?.version;

  /// Retorna informações de todos os livros da Bíblia
  List<Map<String, dynamic>> _getBooksInfo() {
    return [
      // Antigo Testamento
      {'id': 'gn', 'name': 'Gênesis', 'abbreviation': 'Gn', 'isOldTestament': true},
      {'id': 'ex', 'name': 'Êxodo', 'abbreviation': 'Ex', 'isOldTestament': true},
      {'id': 'lv', 'name': 'Levítico', 'abbreviation': 'Lv', 'isOldTestament': true},
      {'id': 'nm', 'name': 'Números', 'abbreviation': 'Nm', 'isOldTestament': true},
      {'id': 'dt', 'name': 'Deuteronômio', 'abbreviation': 'Dt', 'isOldTestament': true},
      {'id': 'js', 'name': 'Josué', 'abbreviation': 'Js', 'isOldTestament': true},
      {'id': 'jud', 'name': 'Juízes', 'abbreviation': 'Jz', 'isOldTestament': true},
      {'id': 'rt', 'name': 'Rute', 'abbreviation': 'Rt', 'isOldTestament': true},
      {'id': '1sm', 'name': '1 Samuel', 'abbreviation': '1Sm', 'isOldTestament': true},
      {'id': '2sm', 'name': '2 Samuel', 'abbreviation': '2Sm', 'isOldTestament': true},
      {'id': '1kgs', 'name': '1 Reis', 'abbreviation': '1Rs', 'isOldTestament': true},
      {'id': '2kgs', 'name': '2 Reis', 'abbreviation': '2Rs', 'isOldTestament': true},
      {'id': '1ch', 'name': '1 Crônicas', 'abbreviation': '1Cr', 'isOldTestament': true},
      {'id': '2ch', 'name': '2 Crônicas', 'abbreviation': '2Cr', 'isOldTestament': true},
      {'id': 'ezr', 'name': 'Esdras', 'abbreviation': 'Ed', 'isOldTestament': true},
      {'id': 'ne', 'name': 'Neemias', 'abbreviation': 'Ne', 'isOldTestament': true},
      {'id': 'et', 'name': 'Ester', 'abbreviation': 'Et', 'isOldTestament': true},
      {'id': 'job', 'name': 'Jó', 'abbreviation': 'Jó', 'isOldTestament': true},
      {'id': 'ps', 'name': 'Salmos', 'abbreviation': 'Sl', 'isOldTestament': true},
      {'id': 'prv', 'name': 'Provérbios', 'abbreviation': 'Pv', 'isOldTestament': true},
      {'id': 'ec', 'name': 'Eclesiastes', 'abbreviation': 'Ec', 'isOldTestament': true},
      {'id': 'so', 'name': 'Cantares', 'abbreviation': 'Ct', 'isOldTestament': true},
      {'id': 'is', 'name': 'Isaías', 'abbreviation': 'Is', 'isOldTestament': true},
      {'id': 'jr', 'name': 'Jeremias', 'abbreviation': 'Jr', 'isOldTestament': true},
      {'id': 'lm', 'name': 'Lamentações', 'abbreviation': 'Lm', 'isOldTestament': true},
      {'id': 'ez', 'name': 'Ezequiel', 'abbreviation': 'Ez', 'isOldTestament': true},
      {'id': 'dn', 'name': 'Daniel', 'abbreviation': 'Dn', 'isOldTestament': true},
      {'id': 'ho', 'name': 'Oséias', 'abbreviation': 'Os', 'isOldTestament': true},
      {'id': 'jl', 'name': 'Joel', 'abbreviation': 'Jl', 'isOldTestament': true},
      {'id': 'am', 'name': 'Amós', 'abbreviation': 'Am', 'isOldTestament': true},
      {'id': 'ob', 'name': 'Obadias', 'abbreviation': 'Ob', 'isOldTestament': true},
      {'id': 'jn', 'name': 'Jonas', 'abbreviation': 'Jn', 'isOldTestament': true},
      {'id': 'mi', 'name': 'Miquéias', 'abbreviation': 'Mq', 'isOldTestament': true},
      {'id': 'na', 'name': 'Naum', 'abbreviation': 'Na', 'isOldTestament': true},
      {'id': 'hk', 'name': 'Habacuque', 'abbreviation': 'Hc', 'isOldTestament': true},
      {'id': 'zp', 'name': 'Sofonias', 'abbreviation': 'Sf', 'isOldTestament': true},
      {'id': 'hg', 'name': 'Ageu', 'abbreviation': 'Ag', 'isOldTestament': true},
      {'id': 'zc', 'name': 'Zacarias', 'abbreviation': 'Zc', 'isOldTestament': true},
      {'id': 'ml', 'name': 'Malaquias', 'abbreviation': 'Ml', 'isOldTestament': true},

      // Novo Testamento
      {'id': 'mt', 'name': 'Mateus', 'abbreviation': 'Mt', 'isOldTestament': false},
      {'id': 'mk', 'name': 'Marcos', 'abbreviation': 'Mc', 'isOldTestament': false},
      {'id': 'lk', 'name': 'Lucas', 'abbreviation': 'Lc', 'isOldTestament': false},
      {'id': 'jo', 'name': 'João', 'abbreviation': 'Jo', 'isOldTestament': false},
      {'id': 'act', 'name': 'Atos', 'abbreviation': 'At', 'isOldTestament': false},
      {'id': 'rm', 'name': 'Romanos', 'abbreviation': 'Rm', 'isOldTestament': false},
      {'id': '1co', 'name': '1 Coríntios', 'abbreviation': '1Co', 'isOldTestament': false},
      {'id': '2co', 'name': '2 Coríntios', 'abbreviation': '2Co', 'isOldTestament': false},
      {'id': 'gl', 'name': 'Gálatas', 'abbreviation': 'Gl', 'isOldTestament': false},
      {'id': 'eph', 'name': 'Efésios', 'abbreviation': 'Ef', 'isOldTestament': false},
      {'id': 'ph', 'name': 'Filipenses', 'abbreviation': 'Fp', 'isOldTestament': false},
      {'id': 'cl', 'name': 'Colossenses', 'abbreviation': 'Cl', 'isOldTestament': false},
      {'id': '1ts', 'name': '1 Tessalonicenses', 'abbreviation': '1Ts', 'isOldTestament': false},
      {'id': '2ts', 'name': '2 Tessalonicenses', 'abbreviation': '2Ts', 'isOldTestament': false},
      {'id': '1tm', 'name': '1 Timóteo', 'abbreviation': '1Tm', 'isOldTestament': false},
      {'id': '2tm', 'name': '2 Timóteo', 'abbreviation': '2Tm', 'isOldTestament': false},
      {'id': 'tt', 'name': 'Tito', 'abbreviation': 'Tt', 'isOldTestament': false},
      {'id': 'phm', 'name': 'Filemom', 'abbreviation': 'Fm', 'isOldTestament': false},
      {'id': 'hb', 'name': 'Hebreus', 'abbreviation': 'Hb', 'isOldTestament': false},
      {'id': 'jm', 'name': 'Tiago', 'abbreviation': 'Tg', 'isOldTestament': false},
      {'id': '1pe', 'name': '1 Pedro', 'abbreviation': '1Pe', 'isOldTestament': false},
      {'id': '2pe', 'name': '2 Pedro', 'abbreviation': '2Pe', 'isOldTestament': false},
      {'id': '1jo', 'name': '1 João', 'abbreviation': '1Jo', 'isOldTestament': false},
      {'id': '2jo', 'name': '2 João', 'abbreviation': '2Jo', 'isOldTestament': false},
      {'id': '3jo', 'name': '3 João', 'abbreviation': '3Jo', 'isOldTestament': false},
      {'id': 'jd', 'name': 'Judas', 'abbreviation': 'Jd', 'isOldTestament': false},
      {'id': 're', 'name': 'Apocalipse', 'abbreviation': 'Ap', 'isOldTestament': false},
    ];
  }
}
