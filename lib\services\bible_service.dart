import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/bible_models.dart';

/// Serviço para carregar e gerenciar dados da Bíblia
class BibleService {
  static final BibleService _instance = BibleService._internal();
  factory BibleService() => _instance;
  BibleService._internal();

  Bible? _bible;
  bool _isLoaded = false;

  /// Carrega a Bíblia do arquivo JSON
  Future<Bible> loadBible() async {
    if (_isLoaded && _bible != null) {
      return _bible!;
    }

    try {
      final String jsonString = await rootBundle.loadString('assets/data/bible.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      _bible = Bible.fromJson(jsonData);
      _isLoaded = true;
      return _bible!;
    } catch (e) {
      throw Exception('Erro ao carregar a Bíblia: $e');
    }
  }

  /// Busca versículos por texto
  Future<List<BibleSearchResult>> searchVerses(String query) async {
    if (query.trim().isEmpty) return [];

    final bible = await loadBible();
    final List<BibleSearchResult> results = [];
    final String lowerQuery = query.toLowerCase();

    for (final book in bible.books) {
      for (final chapter in book.chapters) {
        for (final verse in chapter.verses) {
          if (verse.text.toLowerCase().contains(lowerQuery)) {
            results.add(BibleSearchResult(
              book: book,
              chapter: chapter,
              verse: verse,
            ));
          }
        }
      }
    }

    return results;
  }

  /// Busca um livro específico por nome ou abreviação
  Future<BibleBook?> findBook(String nameOrAbbreviation) async {
    final bible = await loadBible();
    final String query = nameOrAbbreviation.toLowerCase();

    for (final book in bible.books) {
      if (book.name.toLowerCase() == query || 
          book.abbreviation.toLowerCase() == query) {
        return book;
      }
    }

    return null;
  }

  /// Busca um capítulo específico de um livro
  Future<BibleChapter?> findChapter(String bookName, int chapterNumber) async {
    final book = await findBook(bookName);
    if (book == null) return null;

    for (final chapter in book.chapters) {
      if (chapter.number == chapterNumber) {
        return chapter;
      }
    }

    return null;
  }

  /// Busca um versículo específico
  Future<BibleVerse?> findVerse(String bookName, int chapterNumber, int verseNumber) async {
    final chapter = await findChapter(bookName, chapterNumber);
    if (chapter == null) return null;

    for (final verse in chapter.verses) {
      if (verse.number == verseNumber) {
        return verse;
      }
    }

    return null;
  }

  /// Obtém todos os livros
  Future<List<BibleBook>> getAllBooks() async {
    final bible = await loadBible();
    return bible.books;
  }

  /// Obtém livros do Antigo Testamento
  Future<List<BibleBook>> getOldTestamentBooks() async {
    final bible = await loadBible();
    return bible.oldTestamentBooks;
  }

  /// Obtém livros do Novo Testamento
  Future<List<BibleBook>> getNewTestamentBooks() async {
    final bible = await loadBible();
    return bible.newTestamentBooks;
  }

  /// Verifica se a Bíblia está carregada
  bool get isLoaded => _isLoaded;

  /// Obtém a versão da Bíblia
  String? get version => _bible?.version;
}
