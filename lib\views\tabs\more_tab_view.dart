import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_strings.dart';
import '../../utils/app_theme.dart';
import '../../viewmodels/home_viewmodel.dart';
import '../login_view.dart';

class MoreTabView extends StatelessWidget {
  const MoreTabView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final strings = AppStrings();
    final appTheme = AppTheme();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mais',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: appTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildListTile(
                  context,
                  Icons.person,
                  'Perfil',
                  () => _showFeatureNotImplemented(context, strings),
                ),
                _buildListTile(
                  context,
                  Icons.settings,
                  'Configurações',
                  () => _showFeatureNotImplemented(context, strings),
                ),
                _buildListTile(
                  context,
                  Icons.help,
                  'Ajuda',
                  () => _showFeatureNotImplemented(context, strings),
                ),
                _buildListTile(
                  context,
                  Icons.info,
                  'Sobre',
                  () => _showFeatureNotImplemented(context, strings),
                ),
                const Divider(),
                _buildListTile(
                  context,
                  Icons.logout,
                  'Sair',
                  () async {
                    await viewModel.logout();
                    if (context.mounted) {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => const LoginView(),
                        ),
                      );
                    }
                  },
                  color: Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListTile(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap, {
    Color? color,
  }) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: color,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showFeatureNotImplemented(BuildContext context, AppStrings strings) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(strings.featureNotImplemented),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
