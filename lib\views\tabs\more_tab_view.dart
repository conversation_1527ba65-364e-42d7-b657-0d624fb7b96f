import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_strings.dart';
import '../../utils/app_theme.dart';
import '../../viewmodels/home_viewmodel.dart';
import '../login_view.dart';

class MoreTabView extends StatelessWidget {
  const MoreTabView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final user = viewModel.currentUser;
    final strings = AppStrings();
    final appTheme = AppTheme();

    return viewModel.isBusy
        ? const Center(child: CircularProgressIndicator())
        : Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  strings.moreTabTitle,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: appTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 16),
                // Perfil do usuário
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          strings.welcomeText,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          user?.username ?? 'User',
                          style: TextStyle(
                            fontSize: 16,
                            color: appTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          strings.profileInfoText,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Recursos rápidos
                Text(
                  'Recursos rápidos',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: appTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: GridView.count(
                    crossAxisCount: 4,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildFeatureCard(
                        context,
                        Icons.dashboard,
                        strings.dashboardText,
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.settings,
                        strings.settingsText,
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.person,
                        strings.profileText,
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.help,
                        strings.helpText,
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Menu de opções
                Expanded(
                  child: ListView(
                    children: [
                      _buildListTile(
                        context,
                        Icons.person,
                        'Perfil',
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildListTile(
                        context,
                        Icons.settings,
                        'Configurações',
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildListTile(
                        context,
                        Icons.help,
                        'Ajuda',
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      _buildListTile(
                        context,
                        Icons.info,
                        'Sobre',
                        () => _showFeatureNotImplemented(context, strings),
                      ),
                      const Divider(),
                      _buildListTile(
                        context,
                        Icons.logout,
                        'Sair',
                        () async {
                          await viewModel.logout();
                          if (context.mounted) {
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => const LoginView(),
                              ),
                            );
                          }
                        },
                        color: Colors.red,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buildListTile(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap, {
    Color? color,
  }) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: color,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap,
  ) {
    final appTheme = AppTheme();
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 24,
                color: appTheme.primaryColor,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFeatureNotImplemented(BuildContext context, AppStrings strings) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(strings.featureNotImplemented),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
