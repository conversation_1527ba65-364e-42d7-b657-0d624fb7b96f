import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/app_strings.dart';
import '../utils/app_theme.dart';
import '../viewmodels/bible_viewmodel.dart';
import '../models/bible_models.dart';

class BibleView extends StatefulWidget {
  const BibleView({Key? key}) : super(key: key);

  @override
  State<BibleView> createState() => _BibleViewState();
}

class _BibleViewState extends State<BibleView> {
  late BibleViewModel _viewModel;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _viewModel = BibleViewModel();
    _viewModel.loadBooks();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppStrings();
    final appTheme = AppTheme();

    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<BibleViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: appTheme.backgroundColor,
            appBar: AppBar(
              title: Text(_getAppBarTitle(viewModel, strings)),
              backgroundColor: appTheme.primaryColor,
              foregroundColor: appTheme.buttonTextColor,
              leading: _buildBackButton(viewModel),
              actions: [
                if (viewModel.selectedChapter != null) ...[
                  if (viewModel.hasPreviousChapter)
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: viewModel.goToPreviousChapter,
                    ),
                  if (viewModel.hasNextChapter)
                    IconButton(
                      icon: const Icon(Icons.arrow_forward_ios),
                      onPressed: viewModel.goToNextChapter,
                    ),
                ],
              ],
            ),
            body: Column(
              children: [
                // Barra de busca
                if (viewModel.selectedBook == null && viewModel.selectedChapter == null)
                  _buildSearchBar(viewModel, strings, appTheme),
                
                // Conteúdo principal
                Expanded(
                  child: _buildContent(viewModel, strings, appTheme),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: strings.searchVerses,
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    viewModel.clearSearch();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onChanged: (value) {
          viewModel.searchVerses(value);
        },
      ),
    );
  }

  Widget _buildContent(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    if (viewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (viewModel.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: appTheme.errorColor),
            const SizedBox(height: 16),
            Text(
              viewModel.error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: appTheme.errorColor),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: viewModel.loadBooks,
              child: const Text('Tentar novamente'),
            ),
          ],
        ),
      );
    }

    // Resultados de busca
    if (viewModel.searchQuery.isNotEmpty) {
      return _buildSearchResults(viewModel, strings, appTheme);
    }

    // Visualização de versículos
    if (viewModel.selectedChapter != null) {
      return _buildChapterView(viewModel, strings, appTheme);
    }

    // Lista de capítulos
    if (viewModel.selectedBook != null) {
      return _buildChaptersList(viewModel, strings, appTheme);
    }

    // Lista de livros
    return _buildBooksList(viewModel, strings, appTheme);
  }

  Widget _buildSearchResults(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    if (viewModel.isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (viewModel.searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              strings.noResultsFound,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: viewModel.searchResults.length,
      itemBuilder: (context, index) {
        final result = viewModel.searchResults[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(
              result.reference,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: appTheme.primaryColor,
              ),
            ),
            subtitle: Text(result.verse.text),
            onTap: () => viewModel.navigateToSearchResult(result),
          ),
        );
      },
    );
  }

  Widget _buildBooksList(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Antigo Testamento
        if (viewModel.oldTestamentBooks.isNotEmpty) ...[
          Text(
            'Antigo Testamento',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: appTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...viewModel.oldTestamentBooks.map((book) => _buildBookCard(book, viewModel, appTheme)),
          const SizedBox(height: 16),
        ],
        
        // Novo Testamento
        if (viewModel.newTestamentBooks.isNotEmpty) ...[
          Text(
            'Novo Testamento',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: appTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...viewModel.newTestamentBooks.map((book) => _buildBookCard(book, viewModel, appTheme)),
        ],
      ],
    );
  }

  Widget _buildBookCard(BibleBook book, BibleViewModel viewModel, AppTheme appTheme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: appTheme.primaryColor,
          child: Text(
            book.abbreviation,
            style: TextStyle(
              color: appTheme.buttonTextColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          book.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text('${book.chaptersCount} capítulos'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async => await viewModel.selectBook(book),
      ),
    );
  }

  Widget _buildChaptersList(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    final book = viewModel.selectedBook!;
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.2,
      ),
      itemCount: book.chapters.length,
      itemBuilder: (context, index) {
        final chapter = book.chapters[index];
        return Card(
          child: InkWell(
            onTap: () => viewModel.selectChapter(chapter),
            child: Center(
              child: Text(
                '${chapter.number}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: appTheme.primaryColor,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChapterView(BibleViewModel viewModel, AppStrings strings, AppTheme appTheme) {
    final chapter = viewModel.selectedChapter!;
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: chapter.verses.length,
      itemBuilder: (context, index) {
        final verse = chapter.verses[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${verse.number} ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: appTheme.primaryColor,
                  ),
                ),
                TextSpan(
                  text: verse.text,
                  style: TextStyle(
                    fontSize: 16,
                    height: 1.5,
                    color: appTheme.textColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget? _buildBackButton(BibleViewModel viewModel) {
    if (viewModel.selectedChapter != null) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: viewModel.goBackToChapters,
      );
    } else if (viewModel.selectedBook != null) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: viewModel.goBackToBooks,
      );
    }
    return null;
  }

  String _getAppBarTitle(BibleViewModel viewModel, AppStrings strings) {
    if (viewModel.selectedChapter != null) {
      return '${viewModel.selectedBook!.name} ${viewModel.selectedChapter!.number}';
    } else if (viewModel.selectedBook != null) {
      return viewModel.selectedBook!.name;
    } else if (viewModel.searchQuery.isNotEmpty) {
      return strings.searchResults;
    }
    return strings.bibleTitle;
  }
}
