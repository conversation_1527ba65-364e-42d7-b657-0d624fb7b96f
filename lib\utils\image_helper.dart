import 'flavor_config.dart';

/// Helper para carregar imagens específicas de cada flavor
class ImageHelper {
  // Singleton pattern
  static final ImageHelper _instance = ImageHelper._internal();
  factory ImageHelper() => _instance;
  ImageHelper._internal();

  /// Retorna o caminho da imagem com base no flavor atual
  ///
  /// Primeiro tenta carregar a imagem específica do flavor atual.
  /// Se não encontrar, carrega a imagem padrão.
  ///
  /// Exemplo:
  /// ```dart
  /// String logoPath = ImageHelper().getImagePath('logo.png');
  /// ```
  String getImagePath(String imageName) {
    final flavorConfig = FlavorConfig();

    // Caminho específico do flavor
    String flavorSpecificPath;

    switch (flavorConfig.flavor) {
      case Flavor.zionek:
        flavorSpecificPath = 'assets/images/zionek/$imageName';
        break;
      case Flavor.pulsepoint:
        flavorSpecificPath = 'assets/images/pulsepoint/$imageName';
        break;
    }

    // Caminho padrão (compartilhado)
    final defaultPath = 'assets/images/$imageName';

    // Retorna o caminho específico do flavor
    // Na prática, você pode implementar uma verificação se o arquivo existe
    // e retornar o caminho padrão se não existir
    return flavorSpecificPath;
  }

  /// Retorna o caminho do logo com base no flavor atual
  String get logoPath => getImagePath('logo.png');
  /// Retorna o caminho da imagem de fundo superior com base no flavor atual
  String get topBackgroundPath => getImagePath('top_bg.png');
  /// Retorna o caminho da imagem de fundo inferior com base no flavor atual
  String get bottomBackgroundPath => getImagePath('bottom_bg.png');

  /// Retorna o caminho da imagem da toolbar com base no flavor atual
  String get toolbarImagePath => getImagePath('ic_toolbar.png');
}
