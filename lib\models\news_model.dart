import 'package:flutter/material.dart';

/// Modelo para representar uma notícia no aplicativo
class NewsModel {
  final String id;
  final String title;
  final String content;
  final String imageUrl;
  final DateTime publishDate;
  final String author;

  NewsModel({
    required this.id,
    required this.title,
    required this.content,
    required this.imageUrl,
    required this.publishDate,
    required this.author,
  });

  // Método para criar uma lista de notícias de exemplo
  static List<NewsModel> getDummyNews() {
    return [
      NewsModel(
        id: '1',
        title: 'Conferência de Líderes 2023',
        content: 'A Conferência de Líderes 2023 será realizada nos dias 15 a 17 de outubro. Inscrições abertas!',
        imageUrl: 'assets/images/zionek/logo.png', // Usando imagem existente como placeholder
        publishDate: DateTime.now().subtract(const Duration(days: 2)),
        author: 'Equipe de Comunicação',
      ),
      NewsModel(
        id: '2',
        title: 'Campanha de arrecadação de alimentos',
        content: 'Participe da nossa campanha de arrecadação de alimentos para famílias carentes.',
        imageUrl: 'assets/images/zionek/top_bg.png', // Usando imagem existente como placeholder
        publishDate: DateTime.now().subtract(const Duration(days: 5)),
        author: 'Ministério de Ação Social',
      ),
      NewsModel(
        id: '3',
        title: 'Novo horário dos cultos de domingo',
        content: 'A partir do próximo domingo, os cultos serão realizados nos horários: 9h, 11h e 18h.',
        imageUrl: 'assets/images/zionek/bottom_bg.png', // Usando imagem existente como placeholder
        publishDate: DateTime.now().subtract(const Duration(days: 7)),
        author: 'Secretaria',
      ),
    ];
  }
}
