import 'package:flutter/material.dart';
import '../utils/app_theme.dart';
import '../utils/flavor_config.dart';

class ThemeTestView extends StatelessWidget {
  const ThemeTestView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme();
    final flavorConfig = FlavorConfig();

    return Scaffold(
      backgroundColor: appTheme.darkBackgroundColor,
      appBar: AppBar(
        backgroundColor: appTheme.primaryColor,
        title: Text('Teste de Tema - ${flavorConfig.flavorName}'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Flavor Atual: ${flavorConfig.flavorName}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: appTheme.primaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Cor Primária: ${appTheme.primaryColor}',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: appTheme.secondaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Cor Secundária: ${appTheme.secondaryColor}',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: appTheme.backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: appTheme.secondaryColor),
              ),
              child: Text(
                'Cor de Fundo: ${appTheme.backgroundColor}',
                style: TextStyle(color: appTheme.textColor),
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Fundo Escuro Atual: ${appTheme.darkBackgroundColor}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                // Alternar entre flavors para teste
                if (flavorConfig.isZionek) {
                  flavorConfig.setFlavor(Flavor.pulsepoint);
                } else {
                  flavorConfig.setFlavor(Flavor.zionek);
                }
                // Forçar rebuild
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (_) => const ThemeTestView()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: appTheme.secondaryColor,
              ),
              child: Text(
                'Alternar Flavor',
                style: TextStyle(color: appTheme.buttonTextColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
