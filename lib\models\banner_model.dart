import 'package:flutter/material.dart';

/// Modelo para representar um banner de destaque no aplicativo
class BannerModel {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String linkUrl;
  final DateTime startDate;
  final DateTime endDate;

  BannerModel({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.linkUrl,
    required this.startDate,
    required this.endDate,
  });

  // Método para verificar se o banner está ativo
  bool isActive() {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  // Método para criar uma lista de banners de exemplo
  static List<BannerModel> getDummyBanners() {
    final now = DateTime.now();

    return [
      BannerModel(
        id: '1',
        title: 'Oferta Especial',
        description: 'Até 50% de desconto em livros selecionados',
        imageUrl: 'assets/images/ic_banner_teste.png',
        linkUrl: 'https://example.com/offer',
        startDate: now.subtract(const Duration(days: 5)),
        endDate: now.add(const Duration(days: 10)),
      ),
      BannerModel(
        id: '2',
        title: 'Retiro de Jovens',
        description: 'Inscrições abertas para o retiro de jovens',
        imageUrl: 'assets/images/ic_banner_teste.png', // Usando imagem existente como placeholder
        linkUrl: 'https://example.com/retreat',
        startDate: now.subtract(const Duration(days: 2)),
        endDate: now.add(const Duration(days: 15)),
      ),
      BannerModel(
        id: '3',
        title: 'Nova Série de Estudos',
        description: 'Acompanhe nossa nova série de estudos bíblicos',
        imageUrl: 'assets/images/ic_banner_teste.png', // Usando imagem existente como placeholder
        linkUrl: 'https://example.com/studies',
        startDate: now.subtract(const Duration(days: 1)),
        endDate: now.add(const Duration(days: 30)),
      ),
    ];
  }
}
