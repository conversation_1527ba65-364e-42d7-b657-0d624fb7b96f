import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../models/video_model.dart';
import '../utils/app_theme.dart';

class VideoDetailView extends StatefulWidget {
  final VideoModel video;
  final appTheme = AppTheme();

   VideoDetailView({
    Key? key,
    required this.video,
  }) : super(key: key);

  @override
  State<VideoDetailView> createState() => _VideoDetailViewState();
}

class _VideoDetailViewState extends State<VideoDetailView> {
  YoutubePlayerController? _youtubeController;
  bool _isPlayerReady = false;
  bool _showDescription = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _youtubeController?.dispose();
    super.dispose();
  }

  void _initializePlayer() {
    final videoId = YoutubePlayer.convertUrlToId(widget.video.videoUrl);
    
    if (videoId != null) {
      _youtubeController = YoutubePlayerController(
        initialVideoId: videoId,
        flags: const YoutubePlayerFlags(
          autoPlay: false,
          mute: false,
          enableCaption: true,
          captionLanguage: 'pt',
          controlsVisibleAtStart: true,
        ),
      );

      _youtubeController!.addListener(() {
        if (_youtubeController!.value.isReady && !_isPlayerReady) {
          setState(() {
            _isPlayerReady = true;
          });
        }
      });
    }
  }

  void _shareVideo() {
    Share.share(
      '${widget.video.title}\n\n${widget.video.videoUrl}',
      subject: widget.video.title,
    );
  }

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme();

    return Scaffold(
      backgroundColor: appTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: appTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.video.title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Player de vídeo
            if (_youtubeController != null)
              AspectRatio(
                aspectRatio: 16 / 9,
                child: YoutubePlayer(
                  controller: _youtubeController!,
                  showVideoProgressIndicator: true,
                  progressIndicatorColor: appTheme.secondaryColor,
                  progressColors: ProgressBarColors(
                    playedColor: appTheme.secondaryColor,
                    handleColor: appTheme.secondaryColor,
                  ),
                ),
              ),
            
            // Informações do vídeo
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Título
                  Text(
                    widget.video.title,
                    style: TextStyle(
                      color: appTheme.primaryColor,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Autor e categoria
                  Row(
                    children: [
                      Text(
                        'Pastor • ${widget.video.author}',
                        style: TextStyle(
                          color: appTheme.primaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  Text(
                    widget.video.category,
                    style: TextStyle(
                      color: appTheme.primaryColor,
                      fontSize: 14,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                   Divider(color: appTheme.primaryColor),
                  const SizedBox(height: 16),
                  
                  // Botão de compartilhar
                  InkWell(
                    onTap: _shareVideo,
                    child: Row(
                      children: [
                        Icon(
                          Icons.share,
                          color: appTheme.secondaryColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Compartilhar',
                          style: TextStyle(
                            color: appTheme.secondaryColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Seção de áudio (placeholder)
                  Text(
                    'Disponível em áudio',
                    style: TextStyle(
                      color: appTheme.primaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Player de áudio (placeholder)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white70,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        // Barra de progresso
                        Row(
                          children: [
                             Text(
                              '00:00:00',
                              style: TextStyle(
                                color:appTheme.primaryColor,
                                fontSize: 12,
                              ),
                            ),
                            Expanded(
                              child: Slider(
                                value: 0.0,
                                onChanged: (value) {},
                                activeColor: appTheme.secondaryColor,
                                inactiveColor: appTheme.primaryColor,
                              ),
                            ),
                            Text(
                              widget.video.formattedDuration,
                              style:  TextStyle(
                                color: appTheme.primaryColor,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),

                        // Controles de áudio
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.replay_10,
                                color: appTheme.secondaryColor,
                                size: 32,
                              ),
                              onPressed: () {},
                            ),
                            const SizedBox(width: 20),
                            IconButton(
                              icon: Icon(
                                Icons.play_arrow,
                                color: appTheme.secondaryColor,
                                size: 48,
                              ),
                              onPressed: () {},
                            ),
                            const SizedBox(width: 20),
                            IconButton(
                              icon: Icon(
                                Icons.forward_10,
                                color: appTheme.secondaryColor,
                                size: 32,
                              ),
                              onPressed: () {},
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Botões de ação
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {},
                          icon: Icon(
                            Icons.copy,
                            color: appTheme.secondaryColor,
                          ),
                          label: Text(
                            'Copiar',
                            style: TextStyle(
                              color: appTheme.secondaryColor,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: appTheme.secondaryColor),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {},
                          icon: Icon(
                            Icons.bookmark_border,
                            color: appTheme.secondaryColor,
                          ),
                          label: Text(
                            'Salvar como nota',
                            style: TextStyle(
                              color: appTheme.secondaryColor,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: appTheme.secondaryColor),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Controles de texto
                  Row(
                    children: [
                      TextButton.icon(
                        onPressed: () {},
                        icon: Icon(
                          Icons.add,
                          color: appTheme.secondaryColor,
                          size: 16,
                        ),
                        label: Text(
                          'Aa',
                          style: TextStyle(
                            color: appTheme.secondaryColor,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      TextButton.icon(
                        onPressed: () {},
                        icon: Icon(
                          Icons.remove,
                          color: appTheme.secondaryColor,
                          size: 16,
                        ),
                        label: Text(
                          'Aa',
                          style: TextStyle(
                            color: appTheme.secondaryColor,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Descrição
                  Text(
                    widget.video.description,
                    style:  TextStyle(
                      color: appTheme.primaryColor,
                      fontSize: 16,
                      height: 1.5,
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
