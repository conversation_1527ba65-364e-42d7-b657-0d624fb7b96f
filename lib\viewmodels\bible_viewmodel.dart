import 'package:flutter/foundation.dart';
import '../models/bible_models.dart';
import '../services/bible_service.dart';

/// ViewModel para gerenciar o estado da tela da Bíblia
class BibleViewModel extends ChangeNotifier {
  final BibleService _bibleService = BibleService();

  // Estado da aplicação
  bool _isLoading = false;
  String? _error;
  
  // Dados da Bíblia
  List<BibleBook> _books = [];
  BibleBook? _selectedBook;
  BibleChapter? _selectedChapter;
  
  // Busca
  String _searchQuery = '';
  List<BibleSearchResult> _searchResults = [];
  bool _isSearching = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<BibleBook> get books => _books;
  BibleBook? get selectedBook => _selectedBook;
  BibleChapter? get selectedChapter => _selectedChapter;
  String get searchQuery => _searchQuery;
  List<BibleSearchResult> get searchResults => _searchResults;
  bool get isSearching => _isSearching;

  List<BibleBook> get oldTestamentBooks => 
      _books.where((book) => book.isOldTestament).toList();

  List<BibleBook> get newTestamentBooks => 
      _books.where((book) => !book.isOldTestament).toList();

  /// Carrega todos os livros da Bíblia
  Future<void> loadBooks() async {
    _setLoading(true);
    _clearError();

    try {
      _books = await _bibleService.getAllBooks();
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar livros: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Seleciona um livro e carrega seu conteúdo
  Future<void> selectBook(BibleBook book) async {
    _setLoading(true);
    _clearError();

    try {
      // Se o livro não tem capítulos, carrega o conteúdo
      if (book.chapters.isEmpty) {
        final loadedBook = await _bibleService.loadBookContent(book.name);
        _selectedBook = loadedBook;
      } else {
        _selectedBook = book;
      }
      _selectedChapter = null;
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar livro: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Seleciona um capítulo
  void selectChapter(BibleChapter chapter) {
    _selectedChapter = chapter;
    notifyListeners();
  }

  /// Busca versículos
  Future<void> searchVerses(String query) async {
    _searchQuery = query;
    
    if (query.trim().isEmpty) {
      _searchResults = [];
      _isSearching = false;
      notifyListeners();
      return;
    }

    _isSearching = true;
    notifyListeners();

    try {
      _searchResults = await _bibleService.searchVerses(query);
    } catch (e) {
      _setError('Erro ao buscar versículos: $e');
      _searchResults = [];
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// Limpa a busca
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
    _isSearching = false;
    notifyListeners();
  }

  /// Navega para um versículo específico a partir do resultado da busca
  void navigateToSearchResult(BibleSearchResult result) {
    _selectedBook = result.book;
    _selectedChapter = result.chapter;
    clearSearch();
    notifyListeners();
  }

  /// Volta para a lista de livros
  void goBackToBooks() {
    _selectedBook = null;
    _selectedChapter = null;
    notifyListeners();
  }

  /// Volta para a lista de capítulos
  void goBackToChapters() {
    _selectedChapter = null;
    notifyListeners();
  }

  /// Navega para o próximo capítulo
  void goToNextChapter() {
    if (_selectedBook == null || _selectedChapter == null) return;

    final currentChapterIndex = _selectedBook!.chapters.indexOf(_selectedChapter!);
    if (currentChapterIndex < _selectedBook!.chapters.length - 1) {
      _selectedChapter = _selectedBook!.chapters[currentChapterIndex + 1];
      notifyListeners();
    }
  }

  /// Navega para o capítulo anterior
  void goToPreviousChapter() {
    if (_selectedBook == null || _selectedChapter == null) return;

    final currentChapterIndex = _selectedBook!.chapters.indexOf(_selectedChapter!);
    if (currentChapterIndex > 0) {
      _selectedChapter = _selectedBook!.chapters[currentChapterIndex - 1];
      notifyListeners();
    }
  }

  /// Verifica se há próximo capítulo
  bool get hasNextChapter {
    if (_selectedBook == null || _selectedChapter == null) return false;
    final currentChapterIndex = _selectedBook!.chapters.indexOf(_selectedChapter!);
    return currentChapterIndex < _selectedBook!.chapters.length - 1;
  }

  /// Verifica se há capítulo anterior
  bool get hasPreviousChapter {
    if (_selectedBook == null || _selectedChapter == null) return false;
    final currentChapterIndex = _selectedBook!.chapters.indexOf(_selectedChapter!);
    return currentChapterIndex > 0;
  }

  // Métodos auxiliares
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
