
/// Modelo para representar um vídeo no aplicativo
class VideoModel {
  final String id;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String videoUrl;
  final String author;
  final String category;
  final DateTime publishDate;
  final int viewCount;
  final Duration duration;

  VideoModel({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.videoUrl,
    required this.author,
    required this.category,
    required this.publishDate,
    this.viewCount = 0,
    required this.duration,
  });

  // Método para criar uma lista de vídeos de exemplo
  static List<VideoModel> getDummyVideos() {
    return [
      VideoModel(
        id: '1',
        title: 'Uma igreja inabalável, Pr. <PERSON>',
        description: 'Mensagem sobre os fundamentos de uma igreja inabalável',
        thumbnailUrl: '0bCNyPu_ddA', // Usando imagem existente como placeholder
        videoUrl: 'https://www.youtube.com/watch?v=0bCNyPu_ddA',
        author: '<PERSON>r. <PERSON>',
        category: 'Mensagem',
        publishDate: DateTime.now().subtract(const Duration(days: 2)),
        viewCount: 1256,
        duration: const Duration(minutes: 45, seconds: 30),
      ),
      VideoModel(
        id: '2',
        title: 'CULTO DE CELEBRAÇÃO, Pr José Padilha',
        description: 'Culto de celebração com louvor e adoração',
        thumbnailUrl: '0bCNyPu_ddA', // Usando imagem existente como placeholder
        videoUrl: 'https://example.com/videos/video2.mp4',
        author: 'Pr. José Padilha',
        category: 'Celebração',
        publishDate: DateTime.now().subtract(const Duration(days: 5)),
        viewCount: 987,
        duration: const Duration(hours: 1, minutes: 15),
      ),
      VideoModel(
        id: '3',
        title: 'O poder da oração',
        description: 'Estudo bíblico sobre o poder da oração na vida do cristão',
        thumbnailUrl: '0bCNyPu_ddA', // Usando imagem existente como placeholder
        videoUrl: 'https://example.com/videos/video3.mp4',
        author: 'Pr. Carlos Silva',
        category: 'Estudo Bíblico',
        publishDate: DateTime.now().subtract(const Duration(days: 7)),
        viewCount: 756,
        duration: const Duration(minutes: 38, seconds: 45),
      ),
      VideoModel(
        id: '4',
        title: 'Louvor e adoração - Culto de domingo',
        description: 'Momento de louvor e adoração do culto de domingo',
        thumbnailUrl: '0bCNyPu_ddA', // Usando imagem existente como placeholder
        videoUrl: 'https://example.com/videos/video4.mp4',
        author: 'Ministério de Louvor',
        category: 'Louvor',
        publishDate: DateTime.now().subtract(const Duration(days: 10)),
        viewCount: 1432,
        duration: const Duration(minutes: 25, seconds: 15),
      ),
      VideoModel(
        id: '5',
        title: 'Conferência de Jovens 2023',
        description: 'Melhores momentos da conferência de jovens',
        thumbnailUrl: '0bCNyPu_ddA', // Usando imagem existente como placeholder
        videoUrl: 'https://example.com/videos/video5.mp4',
        author: 'Ministério de Jovens',
        category: 'Eventos',
        publishDate: DateTime.now().subtract(const Duration(days: 15)),
        viewCount: 2156,
        duration: const Duration(minutes: 52, seconds: 20),
      ),
    ];
  }

  // Método para formatar a duração do vídeo
  String get formattedDuration {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
}

extension NewsExtension on VideoModel {
  Uri? generateURL() {
    final urlString = 'https://i3.ytimg.com/vi/$thumbnailUrl/maxresdefault.jpg';
    return Uri.tryParse(urlString);
  }

  Uri? generateMinimumURL() {
    final urlString = 'https://img.youtube.com/vi/$thumbnailUrl/mqdefault.jpg';
    return Uri.tryParse(urlString);
  }
}