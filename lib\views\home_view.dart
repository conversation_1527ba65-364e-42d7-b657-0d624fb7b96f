import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/app_strings.dart';
import '../utils/app_theme.dart';
import '../utils/image_helper.dart';
import '../viewmodels/home_viewmodel.dart';

class HomeView extends StatelessWidget {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final strings = AppStrings();
    final appTheme = AppTheme();
    final imageHelper = ImageHelper();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: appTheme.primaryColor,
        title: Image.asset(
          imageHelper.toolbarImagePath,
          height: 30,
          color: appTheme.buttonTextColor,
        ),
        centerTitle: true,
      ),
      body: viewModel.isBusy
          ? const Center(child: CircularProgressIndicator())
          : IndexedStack(
              index: viewModel.currentTabIndex,
              children: viewModel.tabs.map((tab) => tab.screen).toList(),
            ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: viewModel.currentTabIndex,
        onTap: viewModel.setCurrentTab,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: appTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: viewModel.tabs
            .map(
              (tab) => BottomNavigationBarItem(
                icon: Icon(tab.icon),
                label: tab.title,
              ),
            )
            .toList(),
      ),
    );
  }
}
