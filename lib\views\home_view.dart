import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/app_strings.dart';
import '../utils/app_theme.dart';
import '../utils/image_helper.dart';
import '../viewmodels/home_viewmodel.dart';

class HomeView extends StatelessWidget {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final strings = AppStrings();
    final appTheme = AppTheme();
    final imageHelper = ImageHelper();

    return Scaffold(
      appBar: AppBar(
        backgroundColor: appTheme.primaryColor,
        elevation: 0, // Remove a sombra
        title: Image.asset(
          imageHelper.toolbarImagePath,
          height: 170,
        ),
        centerTitle: true,
        actions: [
          // Botão de notificações
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(strings.featureNotImplemented),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          )
          // Botão de pesquisa
          // IconButton(
          //   icon: const Icon(Icons.search, color: Colors.white),
          //   onPressed: () {
          //     ScaffoldMessenger.of(context).showSnackBar(
          //       SnackBar(
          //         content: Text(strings.featureNotImplemented),
          //         duration: const Duration(seconds: 2),
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
      body: viewModel.isBusy
          ? const Center(child: CircularProgressIndicator())
          : IndexedStack(
              index: viewModel.currentTabIndex,
              children: viewModel.tabs.map((tab) => tab.screen).toList(),
            ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: appTheme.primaryColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: viewModel.currentTabIndex,
          onTap: viewModel.setCurrentTab,
          type: BottomNavigationBarType.fixed,
          backgroundColor: appTheme.primaryColor,
          selectedItemColor: Colors.white,
          unselectedItemColor: Colors.white.withOpacity(0.6),
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          elevation: 0, // Remove a sombra padrão
          items: viewModel.tabs.map((tab) {
            final index = viewModel.tabs.indexOf(tab);
            final isSelected = index == viewModel.currentTabIndex;

            return BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: isSelected
                    ? BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                      )
                    : null,
                child: Icon(tab.icon),
              ),
              label: tab.title,
            );
          }).toList(),
        ),
      ),
    );
  }
}
