@echo off
echo Zionek Mobile - Build Script for Flavors
echo =======================================
echo.

if "%1"=="" (
    echo Uso: build_flavor.bat [flavor] [platform] [mode] [action]
    echo.
    echo Flavors disponíveis:
    echo   zionek     - <PERSON><PERSON><PERSON> (padrão)
    echo   pulsepoint - Versão PulsePoint
    echo.
    echo Plataformas disponíveis:
    echo   android - Construir para Android (padrão)
    echo   ios     - Construir para iOS
    echo   all     - Construir para todas as plataformas
    echo.
    echo Modos disponíveis:
    echo   debug   - Modo de depuração (padrão)
    echo   release - Modo de produção
    echo.
    echo Ações disponíveis:
    echo   build   - Construir o aplicativo (padrão)
    echo   run     - Executar o aplicativo
    echo.
    echo Exemplos:
    echo   build_flavor.bat zionek android debug build
    echo   build_flavor.bat pulsepoint android release build
    echo   build_flavor.bat zionek ios release build
    echo   build_flavor.bat pulsepoint all release build
    echo   build_flavor.bat zionek android debug run
    echo   build_flavor.bat pulsepoint android debug run
    exit /b 1
)

set FLAVOR=%1
set PLATFORM=%2
set MODE=%3
set ACTION=%4

if "%FLAVOR%"=="" set FLAVOR=zionek
if "%PLATFORM%"=="" set PLATFORM=android
if "%MODE%"=="" set MODE=debug
if "%ACTION%"=="" set ACTION=build

echo Flavor: %FLAVOR%
echo Plataforma: %PLATFORM%
echo Modo: %MODE%
echo Ação: %ACTION%
echo.

if "%PLATFORM%"=="android" (
    if "%ACTION%"=="build" (
        call :build_android
    ) else if "%ACTION%"=="run" (
        call :run_android
    ) else (
        echo Ação inválida: %ACTION%
        exit /b 1
    )
) else if "%PLATFORM%"=="ios" (
    if "%ACTION%"=="build" (
        call :build_ios
    ) else if "%ACTION%"=="run" (
        call :run_ios
    ) else (
        echo Ação inválida: %ACTION%
        exit /b 1
    )
) else if "%PLATFORM%"=="all" (
    if "%ACTION%"=="build" (
        call :build_android
        call :build_ios
    ) else if "%ACTION%"=="run" (
        echo Não é possível executar em todas as plataformas simultaneamente.
        echo Por favor, escolha uma plataforma específica para executar.
        exit /b 1
    ) else (
        echo Ação inválida: %ACTION%
        exit /b 1
    )
) else (
    echo Plataforma inválida: %PLATFORM%
    exit /b 1
)

echo.
echo Operação concluída!
exit /b 0

:build_android
echo Construindo para Android...
if "%MODE%"=="debug" (
    flutter build apk --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart --debug
) else (
    flutter build apk --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart --release
)
exit /b 0

:run_android
echo Executando no Android...
flutter run --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart
exit /b 0

:build_ios
echo Construindo para iOS...
if "%MODE%"=="debug" (
    flutter build ios --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart --debug --no-codesign
) else (
    flutter build ios --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart --release --no-codesign
)
exit /b 0

:run_ios
echo Executando no iOS...
flutter run --flavor %FLAVOR% -t lib/main_%FLAVOR%.dart
exit /b 0
