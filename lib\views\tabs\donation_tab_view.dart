import 'package:flutter/material.dart';
import '../../utils/app_strings.dart';
import '../../utils/app_theme.dart';

class DonationTabView extends StatelessWidget {
  const DonationTabView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final strings = AppStrings();
    final appTheme = AppTheme();

    return Scaffold(
      backgroundColor: appTheme.backgroundColor, // Fundo escuro como na imagem
      // appBar: AppBar(
      //   backgroundColor: const Color(0xFF1A1A1A),
      //   elevation: 0,
      //   title: Text(
      //     strings.contributeTitle,
      //     style: const TextStyle(
      //       color: Colors.white,
      //       fontSize: 18,
      //       fontWeight: FontWeight.w500,
      //     ),
      //   ),
      //   centerTitle: true,
      //   actions: [
      //     IconButton(
      //       icon: const Icon(Icons.menu, color: Colors.white),
      //       onPressed: () {
      //         // Implementar menu se necessário
      //       },
      //     ),
      //   ],
      // ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            // Seção de Transferência Bancária
            _buildProjectsSection(strings, appTheme),
            const SizedBox(height: 32),

            // Seção de Projetos e Ações
            _buildBankTransferSection(strings, appTheme),
          ],
        ),
      ),
    );
  }

  Widget _buildBankTransferSection(AppStrings strings, AppTheme appTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          strings.bankTransferTitle,
          style: TextStyle(
            color: appTheme.primaryColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // Implementar navegação para contas bancárias
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: appTheme.secondaryColor, // Verde como na imagem
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              strings.viewBankAccountsButton,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProjectsSection(AppStrings strings, AppTheme appTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          strings.projectsAndActionsTitle,
          style: TextStyle(
            color: appTheme.primaryColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildDonationCard(
                title: strings.tithesTitle,
                description: strings.tithesDescription,
                buttonText: strings.collaborateButton,
                onPressed: () {
                  // Implementar ação de dízimos
                },
                isLeftCard: true, appTheme: appTheme
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDonationCard(
                title: strings.offeringsTitle,
                description: strings.offeringsDescription,
                buttonText: strings.collaborateButton,
                onPressed: () {
                  // Implementar ação de ofertas
                },
                isLeftCard: false,
                appTheme: appTheme,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDonationCard({
    required String title,
    required String description,
    required String buttonText,
    required VoidCallback onPressed,
    required bool isLeftCard,
    required AppTheme appTheme
  }) {
    return Container(
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isLeftCard
            ? [
                Colors.grey.shade700,
                Colors.grey.shade900,
              ]
            : [
                Colors.grey.shade600,
                Colors.grey.shade800,
              ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Ícone de compartilhar no canto superior direito
            Align(
              alignment: Alignment.topRight,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.share,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),

            const Spacer(),

            // Texto descritivo
            Text(
              description,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                height: 1.3,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 16),

            // Título
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Botão Colabore
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: appTheme.secondaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.favorite, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      buttonText,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
