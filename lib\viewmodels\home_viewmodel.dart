import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/tab_item_model.dart';
import '../services/auth_service.dart';
import '../utils/app_strings.dart';
import '../views/tabs/home_tab_view.dart';
import '../views/tabs/videos_tab_view.dart';
import '../views/tabs/events_tab_view.dart';
import '../views/tabs/donation_tab_view.dart';
import '../views/tabs/more_tab_view.dart';
import 'base_viewmodel.dart';

class HomeViewModel extends BaseViewModel {
  final AuthService _authService;
  int _currentTabIndex = 0;

  HomeViewModel(this._authService) {
    _initTabs();
  }

  User? get currentUser => _authService.currentUser;
  bool get isAuthenticated => _authService.isAuthenticated;
  int get currentTabIndex => _currentTabIndex;
  List<TabItemModel> _tabs = [];
  List<TabItemModel> get tabs => _tabs;

  void _initTabs() {
    final strings = AppStrings();
    _tabs = [
      TabItemModel(
        title: strings.homeTabTitle,
        icon: Icons.home,
        screen: const HomeTabView(),
      ),
      TabItemModel(
        title: strings.videosTabTitle,
        icon: Icons.play_circle_outline,
        screen: const VideosTabView(),
      ),
      TabItemModel(
        title: strings.eventsTabTitle,
        icon: Icons.event,
        screen: const EventsTabView(),
      ),
      TabItemModel(
        title: strings.donationTabTitle,
        icon: Icons.favorite,
        screen: const DonationTabView(),
      ),
      TabItemModel(
        title: strings.moreTabTitle,
        icon: Icons.more_horiz,
        screen: const MoreTabView(),
      ),
    ];
  }

  void setCurrentTab(int index) {
    if (index != _currentTabIndex) {
      _currentTabIndex = index;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    setBusy();
    try {
      await _authService.logout();
      setIdle();
    } catch (e) {
      setError('Logout failed: ${e.toString()}');
    }
  }
}
