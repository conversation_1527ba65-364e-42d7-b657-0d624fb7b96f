/// Modelos de dados para a Bíblia
class BibleVerse {
  final int number;
  final String text;

  BibleVerse({
    required this.number,
    required this.text,
  });

  factory BibleVerse.fromJson(Map<String, dynamic> json) {
    return BibleVerse(
      number: json['number'] as int,
      text: json['text'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
    };
  }
}

class BibleChapter {
  final int number;
  final List<BibleVerse> verses;

  BibleChapter({
    required this.number,
    required this.verses,
  });

  factory BibleChapter.fromJson(Map<String, dynamic> json) {
    return BibleChapter(
      number: json['number'] as int,
      verses: (json['verses'] as List)
          .map((verse) => BibleVerse.fromJson(verse))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'verses': verses.map((verse) => verse.toJson()).toList(),
    };
  }
}

class BibleBook {
  final String name;
  final String abbreviation;
  final bool isOldTestament;
  final List<BibleChapter> chapters;

  BibleBook({
    required this.name,
    required this.abbreviation,
    required this.isOldTestament,
    required this.chapters,
  });

  factory BibleBook.fromJson(Map<String, dynamic> json) {
    return BibleBook(
      name: json['name'] as String,
      abbreviation: json['abbreviation'] as String,
      isOldTestament: json['isOldTestament'] as bool,
      chapters: (json['chapters'] as List)
          .map((chapter) => BibleChapter.fromJson(chapter))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'abbreviation': abbreviation,
      'isOldTestament': isOldTestament,
      'chapters': chapters.map((chapter) => chapter.toJson()).toList(),
    };
  }

  int get chaptersCount => chapters.length;
}

class Bible {
  final String version;
  final List<BibleBook> books;

  Bible({
    required this.version,
    required this.books,
  });

  factory Bible.fromJson(Map<String, dynamic> json) {
    return Bible(
      version: json['version'] as String,
      books: (json['books'] as List)
          .map((book) => BibleBook.fromJson(book))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'books': books.map((book) => book.toJson()).toList(),
    };
  }

  List<BibleBook> get oldTestamentBooks => 
      books.where((book) => book.isOldTestament).toList();

  List<BibleBook> get newTestamentBooks => 
      books.where((book) => !book.isOldTestament).toList();
}

/// Resultado de busca de versículos
class BibleSearchResult {
  final BibleBook book;
  final BibleChapter chapter;
  final BibleVerse verse;

  BibleSearchResult({
    required this.book,
    required this.chapter,
    required this.verse,
  });

  String get reference => '${book.name} ${chapter.number}:${verse.number}';
}
