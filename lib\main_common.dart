import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'services/auth_service.dart';
import 'utils/app_strings.dart';
import 'utils/app_theme.dart';
import 'utils/flavor_config.dart';
import 'viewmodels/login_viewmodel.dart';
import 'viewmodels/home_viewmodel.dart';
import 'views/login_view.dart';
import 'views/home_view.dart';

/// Ponto de entrada comum para todos os flavors
/// Recebe o flavor como parâmetro
void mainCommon(Flavor flavor) {
  // Configurar o flavor
  FlavorConfig().setFlavor(flavor);

  // Iniciar o aplicativo
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppStrings();
    final appTheme = AppTheme();
    final flavorConfig = FlavorConfig();

    return MultiProvider(
      providers: [
        // Register services
        ChangeNotifierProvider(create: (_) => AuthService()),

        // Register ViewModels
        ChangeNotifierProxyProvider<AuthService, LoginViewModel>(
          create: (context) => LoginViewModel(
            Provider.of<AuthService>(context, listen: false),
          ),
          update: (context, authService, previous) =>
            previous ?? LoginViewModel(authService),
        ),
        ChangeNotifierProxyProvider<AuthService, HomeViewModel>(
          create: (context) => HomeViewModel(
            Provider.of<AuthService>(context, listen: false),
          ),
          update: (context, authService, previous) =>
            previous ?? HomeViewModel(authService),
        ),
      ],
      child: MaterialApp(
        title: strings.appName,
        theme: appTheme.getTheme(),
        home: const AppStartPage(),
        debugShowCheckedModeBanner: false, // Remover banner de debug
        builder: (context, child) {
          // Adicionar um indicador de flavor no modo de debug
          return Banner(
            message: flavorConfig.flavorName,
            location: BannerLocation.topEnd,
            color: appTheme.secondaryColor,
            child: child!,
          );
        },
      ),
    );
  }
}

class AppStartPage extends StatefulWidget {
  const AppStartPage({super.key});

  @override
  State<AppStartPage> createState() => _AppStartPageState();
}

class _AppStartPageState extends State<AppStartPage> {
  @override
  void initState() {
    super.initState();
    // Check if user is already logged in
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkLoginStatus();
    });
  }

  Future<void> _checkLoginStatus() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final isLoggedIn = await authService.tryAutoLogin();

    if (context.mounted) {
      if (isLoggedIn) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const HomeView()),
        );
      } else {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginView()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
