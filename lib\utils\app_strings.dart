/// Classe que centraliza todas as strings do aplicativo
/// Isso facilita a manutenção, internacionalização e personalização para white labels
class AppStrings {
  // Singleton pattern
  static final AppStrings _instance = AppStrings._internal();
  factory AppStrings() => _instance;
  AppStrings._internal();

  // Variável para armazenar o white label atual
  String _currentWhiteLabel = 'default';

  // Getter para o white label atual
  String get currentWhiteLabel => _currentWhiteLabel;

  // Método para definir o white label
  void setWhiteLabel(String whiteLabel) {
    _currentWhiteLabel = whiteLabel;
  }

  // Strings gerais do aplicativo
  String get appName => _getStringForWhiteLabel({
    'default': 'Zionek',
    'pulsepoint': 'PulsePoint',
    'other_brand': 'OtherBrandName',
  });

  // Strings da tela de login
  String get loginTitle => _getStringForWhiteLabel({
    'default': 'Realize o login e fique por dentro das atividades de sua igreja',
    'pulsepoint': 'Realize o login e fique por dentro das atividades de sua igreja',
    'other_brand': 'Faça login para acessar sua conta',
  });

  String get emailLabel => _getStringForWhiteLabel({
    'default': 'E-mail',
    'pulsepoint': 'E-mail',
    'other_brand': 'E-mail',
  });

  String get passwordLabel => _getStringForWhiteLabel({
    'default': 'Senha',
    'pulsepoint': 'Senha',
    'other_brand': 'Senha',
  });

  String get loginButtonText => _getStringForWhiteLabel({
    'default': 'ENTRAR',
    'pulsepoint': 'ENTRAR',
    'other_brand': 'ACESSAR',
  });

  String get forgotPasswordText => _getStringForWhiteLabel({
    'default': 'Esqueceu sua senha?',
    'pulsepoint': 'Esqueceu sua senha?',
    'other_brand': 'Esqueceu a senha?',
  });

  String get recoverPasswordText => _getStringForWhiteLabel({
    'default': 'Recuperar agora',
    'pulsepoint': 'Recuperar agora',
    'other_brand': 'Recuperar',
  });

  String get forgotPasswordNotImplemented => _getStringForWhiteLabel({
    'default': 'Recuperação de senha não implementada ainda',
    'pulsepoint': 'Recuperação de senha não implementada ainda',
    'other_brand': 'Função de recuperação de senha em desenvolvimento',
  });

  String get demoCredentials => _getStringForWhiteLabel({
    'default': 'Demo: <EMAIL> / password',
    'pulsepoint': 'Demo: <EMAIL> / password',
    'other_brand': 'Demo: <EMAIL> / password',
  });

  // Strings da tela inicial
  String get homeTitle => _getStringForWhiteLabel({
    'default': 'Início',
    'pulsepoint': 'Home',
    'other_brand': 'Início',
  });

  String get welcomeText => _getStringForWhiteLabel({
    'default': 'Bem-vindo',
    'pulsepoint': 'Welcome',
    'other_brand': 'Bem-vindo',
  });

  String get profileInfoText => _getStringForWhiteLabel({
    'default': 'Suas Informações de Perfil:',
    'pulsepoint': 'Your Profile Information:',
    'other_brand': 'Suas Informações de Perfil:',
  });

  String get emailText => _getStringForWhiteLabel({
    'default': 'E-mail',
    'pulsepoint': 'Email',
    'other_brand': 'E-mail',
  });

  String get userIdText => _getStringForWhiteLabel({
    'default': 'ID do Usuário',
    'pulsepoint': 'User ID',
    'other_brand': 'ID do Usuário',
  });

  String get whatToDoText => _getStringForWhiteLabel({
    'default': 'O que você gostaria de fazer hoje?',
    'pulsepoint': 'What would you like to do today?',
    'other_brand': 'O que você gostaria de fazer hoje?',
  });

  String get dashboardText => _getStringForWhiteLabel({
    'default': 'Painel',
    'pulsepoint': 'Dashboard',
    'other_brand': 'Painel',
  });

  String get settingsText => _getStringForWhiteLabel({
    'default': 'Configurações',
    'pulsepoint': 'Settings',
    'other_brand': 'Configurações',
  });

  String get profileText => _getStringForWhiteLabel({
    'default': 'Perfil',
    'pulsepoint': 'Profile',
    'other_brand': 'Perfil',
  });

  String get helpText => _getStringForWhiteLabel({
    'default': 'Ajuda',
    'pulsepoint': 'Help',
    'other_brand': 'Ajuda',
  });

  String get featureNotImplemented => _getStringForWhiteLabel({
    'default': 'Esta funcionalidade ainda não foi implementada',
    'pulsepoint': 'This feature is not implemented yet',
    'other_brand': 'Esta funcionalidade ainda não foi implementada',
  });

  // Strings da Tab Bar
  String get homeTabTitle => _getStringForWhiteLabel({
    'default': 'Home',
    'pulsepoint': 'Home',
    'other_brand': 'Home',
  });

  String get videosTabTitle => _getStringForWhiteLabel({
    'default': 'Vídeos',
    'pulsepoint': 'Videos',
    'other_brand': 'Vídeos',
  });

  String get eventsTabTitle => _getStringForWhiteLabel({
    'default': 'Eventos',
    'pulsepoint': 'Events',
    'other_brand': 'Eventos',
  });

  String get donationTabTitle => _getStringForWhiteLabel({
    'default': 'Doação',
    'pulsepoint': 'Donation',
    'other_brand': 'Doação',
  });

  String get moreTabTitle => _getStringForWhiteLabel({
    'default': 'Mais',
    'pulsepoint': 'More',
    'other_brand': 'Mais',
  });

  String get loginTitleText => _getStringForWhiteLabel({
    'default': '',
    'pulsepoint': 'Login',
    'other_brand': 'Login',
  });

  String get loadingText => _getStringForWhiteLabel({
    'default': 'Entrando...',
    'pulsepoint': 'Logging in...',
    'other_brand': 'Entrando...',
  });

  // Strings da tela de contribuição
  String get contributeTitle => _getStringForWhiteLabel({
    'default': 'Contribua',
    'pulsepoint': 'Contribute',
    'other_brand': 'Contribua',
  });

  String get bankTransferTitle => _getStringForWhiteLabel({
    'default': 'Transferência bancária',
    'pulsepoint': 'Bank Transfer',
    'other_brand': 'Transferência bancária',
  });

  String get viewBankAccountsButton => _getStringForWhiteLabel({
    'default': 'Veja nossas contas bancárias',
    'pulsepoint': 'View our bank accounts',
    'other_brand': 'Veja nossas contas bancárias',
  });

  String get projectsAndActionsTitle => _getStringForWhiteLabel({
    'default': 'Projetos e Ações',
    'pulsepoint': 'Projects and Actions',
    'other_brand': 'Projetos e Ações',
  });

  String get tithesTitle => _getStringForWhiteLabel({
    'default': 'Dízimos',
    'pulsepoint': 'Tithes',
    'other_brand': 'Dízimos',
  });

  String get offeringsTitle => _getStringForWhiteLabel({
    'default': 'Ofertas',
    'pulsepoint': 'Offerings',
    'other_brand': 'Ofertas',
  });

  String get tithesDescription => _getStringForWhiteLabel({
    'default': 'Confie a Deus as suas necessidades e dê o seu primeiro 10% de volta a ele.',
    'pulsepoint': 'Trust God with your needs and give your first 10% back to him.',
    'other_brand': 'Confie a Deus as suas necessidades e dê o seu primeiro 10% de volta a ele.',
  });

  String get offeringsDescription => _getStringForWhiteLabel({
    'default': 'Aquele que semeia com fartura, muito colherá.',
    'pulsepoint': 'He who sows abundantly will reap much.',
    'other_brand': 'Aquele que semeia com fartura, muito colherá.',
  });

  String get collaborateButton => _getStringForWhiteLabel({
    'default': 'Colabore',
    'pulsepoint': 'Collaborate',
    'other_brand': 'Colabore',
  });

  // Strings da Bíblia
  String get bibleTitle => _getStringForWhiteLabel({
    'default': 'Bíblia',
    'pulsepoint': 'Bible',
    'other_brand': 'Bíblia',
  });

  String get bibleDescription => _getStringForWhiteLabel({
    'default': 'Leia e estude a Palavra de Deus',
    'pulsepoint': 'Read and study the Word of God',
    'other_brand': 'Leia e estude a Palavra de Deus',
  });

  String get searchVerses => _getStringForWhiteLabel({
    'default': 'Buscar versículos...',
    'pulsepoint': 'Search verses...',
    'other_brand': 'Buscar versículos...',
  });

  String get booksTitle => _getStringForWhiteLabel({
    'default': 'Livros',
    'pulsepoint': 'Books',
    'other_brand': 'Livros',
  });

  String get chaptersTitle => _getStringForWhiteLabel({
    'default': 'Capítulos',
    'pulsepoint': 'Chapters',
    'other_brand': 'Capítulos',
  });

  String get versesTitle => _getStringForWhiteLabel({
    'default': 'Versículos',
    'pulsepoint': 'Verses',
    'other_brand': 'Versículos',
  });

  String get searchResults => _getStringForWhiteLabel({
    'default': 'Resultados da busca',
    'pulsepoint': 'Search results',
    'other_brand': 'Resultados da busca',
  });

  String get noResultsFound => _getStringForWhiteLabel({
    'default': 'Nenhum resultado encontrado',
    'pulsepoint': 'No results found',
    'other_brand': 'Nenhum resultado encontrado',
  });

  String get chapter => _getStringForWhiteLabel({
    'default': 'Capítulo',
    'pulsepoint': 'Chapter',
    'other_brand': 'Capítulo',
  });

  String get verse => _getStringForWhiteLabel({
    'default': 'Versículo',
    'pulsepoint': 'Verse',
    'other_brand': 'Versículo',
  });

  // Validação de formulários
  String get emailRequired => _getStringForWhiteLabel({
    'default': 'O e-mail é obrigatório',
    'pulsepoint': 'Email is required',
    'other_brand': 'O e-mail é obrigatório',
  });

  String get invalidEmail => _getStringForWhiteLabel({
    'default': 'Digite um endereço de e-mail válido',
    'pulsepoint': 'Enter a valid email address',
    'other_brand': 'Digite um endereço de e-mail válido',
  });

  String get passwordRequired => _getStringForWhiteLabel({
    'default': 'A senha é obrigatória',
    'pulsepoint': 'Password is required',
    'other_brand': 'A senha é obrigatória',
  });

  String get passwordTooShort => _getStringForWhiteLabel({
    'default': 'A senha deve ter pelo menos 6 caracteres',
    'pulsepoint': 'Password must be at least 6 characters',
    'other_brand': 'A senha deve ter pelo menos 6 caracteres',
  });

  // Método auxiliar para obter a string correta com base no white label atual
  String _getStringForWhiteLabel(Map<String, String> strings) {
    return strings[_currentWhiteLabel] ?? strings['default'] ?? 'String not found';
  }
}
