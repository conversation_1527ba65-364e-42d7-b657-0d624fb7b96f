import 'package:flutter/material.dart';
import '../utils/app_strings.dart';
import '../utils/app_theme.dart';
import '../utils/page_transitions.dart';
import 'home_view.dart';

/// Tela de carregamento exibida após o login bem-sucedido
class LoadingView extends StatefulWidget {
  const LoadingView({Key? key}) : super(key: key);

  @override
  State<LoadingView> createState() => _LoadingViewState();
}

class _LoadingViewState extends State<LoadingView> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.forward();

    // Navegar para a tela Home após a animação
    Future.delayed(const Duration(milliseconds: 1800), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          PageTransitions.fadeTransition(const HomeView()),
        );
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme();
    final strings = AppStrings();

    return Scaffold(
      backgroundColor: appTheme.primaryColor,
      body: Center(
        child: FadeTransition(
          opacity: _animation,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo ou ícone animado
              ScaleTransition(
                scale: _animation,
                child: Icon(
                  Icons.check_circle_outline,
                  size: 100,
                  color: appTheme.buttonTextColor,
                ),
              ),
              const SizedBox(height: 24),
              // Texto de carregamento
              Text(
                strings.loadingText,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: appTheme.buttonTextColor,
                ),
              ),
              const SizedBox(height: 32),
              // Indicador de progresso
              SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(appTheme.buttonTextColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
