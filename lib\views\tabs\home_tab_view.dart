import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_strings.dart';
import '../../utils/app_theme.dart';
import '../../viewmodels/home_viewmodel.dart';

class HomeTabView extends StatelessWidget {
  const HomeTabView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<HomeViewModel>(context);
    final user = viewModel.currentUser;
    final strings = AppStrings();
    final appTheme = AppTheme();

    return viewModel.isBusy
        ? const Center(child: CircularProgressIndicator())
        : Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          strings.welcomeText,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          user?.username ?? 'User',
                          style: TextStyle(
                            fontSize: 18,
                            color: appTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          strings.profileInfoText,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ListTile(
                          leading: const Icon(Icons.email),
                          title: Text(strings.emailText),
                          subtitle: Text(user?.email ?? 'No email'),
                          dense: true,
                        ),
                        ListTile(
                          leading: const Icon(Icons.verified_user),
                          title: Text(strings.userIdText),
                          subtitle: Text(user?.id ?? 'No ID'),
                          dense: true,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  strings.whatToDoText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    children: [
                      _buildFeatureCard(
                        context,
                        Icons.dashboard,
                        strings.dashboardText,
                        () {
                          _showFeatureNotImplemented(context, strings);
                        },
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.settings,
                        strings.settingsText,
                        () {
                          _showFeatureNotImplemented(context, strings);
                        },
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.person,
                        strings.profileText,
                        () {
                          _showFeatureNotImplemented(context, strings);
                        },
                      ),
                      _buildFeatureCard(
                        context,
                        Icons.help,
                        strings.helpText,
                        () {
                          _showFeatureNotImplemented(context, strings);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    IconData icon,
    String title,
    VoidCallback onTap,
  ) {
    final appTheme = AppTheme();
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: appTheme.primaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFeatureNotImplemented(BuildContext context, AppStrings strings) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(strings.featureNotImplemented),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
